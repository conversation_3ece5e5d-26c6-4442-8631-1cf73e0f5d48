.checkout h5 {
    font-weight: 700;
    margin: 0
}

.hr {
    height: 1px;
    background-color: #ddd
}

.top {
    background-color: #f1f1f1
}
.search {
    position: absolute;
    right: 0;
    top: 22px;
    font-size: 16px
}

.search .btn-danger {
    font-size: 16px
}

.checkout {
    margin: 20px 0
}

.checkout-steps {
    border: 1px solid #ddd;
}

.seven {
    color: #c81623;
    margin-top: 20px
}

.price {
    font: 14px "微软雅黑";
    font-weight: 700;
    color: #e12228
}

ul.addr-detail li {
    width: 99%;
    margin: 6px 0
}

.recommendAddr {
    margin-top: 10PX
}

ul.payType li {
    display: inline-block;
    padding: 5px 20px;
    border: 1px solid #ddd;
    *display: inline;
    _zoom: 1;
    *margin: 5px 10px;
    cursor: pointer
}

.addr-item .name {
    width: 120px;
    border: 1px solid #ddd;
    height: 18px;
    padding: 5px 10px;
    text-align: center
}

ul.send-detail li {
    margin-top: 10px
}

.sendType .express {
    border: 1px solid #ddd;
    width: 120px;
    text-align: center
}

.sendType, .sendGoods {
    padding: 15px
}

.sendType {
    background: #f4f4f4;
    margin-bottom: 2px
}

.sendGoods {
    background: #feedef
}

.num, .exit {
    text-align: center
}

.order-summary {
    overflow: hidden;
    padding-right: 20px
}

.list, .trade {
    line-height: 26px
}

.list span {
    float: left;
    width: 160px
}

.trade {
    padding: 10px;
    margin: 10px 0;
    text-align: right;
    background-color: #f4f4f4;
    border: 1px solid #ddd
}

.trade .fc-receiverInfo {
    color: #999
}

.pay {
    font-family: "微软雅黑"
}

.pay .orange {
    color: #ea4d08
}

.pay .money {
    font-size: 18px
}

.pay .checkout-tit {
    padding: 10px 60px
}

.pay .paymark {
    overflow: hidden;
    line-height: 26px;
    text-indent: 38px
}

.pay .success-icon {
    width: 30px;
    height: 30px;
    display: inline-block;
    background: url(../img/icon.png);
    background-position: 0 0
}

.pay .success-info {
    padding: 0 8px;
    line-height: 30px;
    vertical-align: top
}

.submit .btn-xlarge {
    padding: 15px 45px;
    margin: 15px 0 10px;
    font: 18px "微软雅黑";
    font-weight: 700;
    border-radius: 0
}

.check-info {
    padding-left: 25px;
    padding-bottom: 15px;
    margin-bottom: 10px;
    border: 2px solid #c81523
}

.check-info h4 {
    color: #c81523
}

.check-info .save {
    font-size: 18px;
    font-weight: 700;
    color: #c81523
}

.check-info ol, .check-info ul {
    padding-left: 25px
}

.zfb {
    color: #c81523;
    font-weight: 700
}

.check-info li {
    line-height: 24px;
    font-size: 14px
}

.weixin {
    line-height: 27px;
    margin-right: 40px;
    font-size: 16px
}

.checkout-steps {
    border: 1px solid #ddd;
    width: 990px;
    margin: 0 auto
}

.checkout-steps .phone {
    background: url(../img/phone-bg.png) no-repeat;
    width: 350px;
    height: 400px;
    margin-left: 40px
}

.checkout-steps .red {
    color: red
}

.checkout-steps .saosao {
    background: url(../img/icon-red.png) 50px 8px no-repeat #ff7674;
    margin-top: 15px;
    padding: 8px 0 8px 125px
}

.checkout-steps .saosao p {
    margin-bottom: 5px;
    color: #fff;
    line-height: 20px;
    margin-top: 0;
    font-size: 15px
}