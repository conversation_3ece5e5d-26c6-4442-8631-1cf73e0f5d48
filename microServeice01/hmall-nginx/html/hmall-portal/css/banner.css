#toggle-button{
    display: none;
}
.button-label{
    position: relative;
    display: inline-block;
    width: 68px;
    background-color: #ccc;
    border: #ccc;
    border-radius: 30px;
    cursor: pointer;
    box-shadow:inset 0 0 3px 1px rgba(0, 0, 0, 0.4);
}
.circle{
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.5);
}
.button-label .text {
    line-height: 20px;
    font-size: 12px;

    /*
    用来阻止页面文字被选中，出现蓝色
    可以将下面两行代码注释掉来查看区别
    */
    -webkit-user-select: none;
    user-select: none;
}
.on {
    color: #fff;
    display: none;
    text-indent: 0;
}
.off {
    color: #fff;
    display: inline-block;
    text-indent: 0;
}
.button-label .circle{
    left: 0;
    transition: all 0.3s;/*transition过度，时间为0.3秒*/
}

/*
以下是checked被选中后，紧跟checked标签后面label的样式。
例如：div+p 选择所有紧接着<div>元素之后的<p>元素
*/
#toggle-button:checked + label.button-label .circle{
    left: 49px;
}
#toggle-button:checked + label.button-label .on{
    display: inline-block;
}
#toggle-button:checked + label.button-label .off{
    display: none;
}
#toggle-button:checked + label.button-label{
    background-color: #FF8800;
}


.banner{
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
}

.logo{
    width: 150px;
}