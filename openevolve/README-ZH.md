# OpenEvolve

Google DeepMind论文"AlphaEvolve: A coding agent for scientific and algorithmic discovery"（2025）中描述的AlphaEvolve系统的开源实现。

![OpenEvolve Logo](openevolve-logo.png)

## 概述

OpenEvolve是一个进化编码代理，它使用大型语言模型通过迭代过程来优化代码。它编排了一个基于LLM的代码生成、评估和选择的流水线，以持续改进各种任务的程序。

主要特性：
- 进化整个代码文件，而不仅仅是单个函数
- 支持多种编程语言
- 支持任何LLM的OpenAI兼容API
- 多目标优化
- 灵活的提示工程
- 分布式评估

## 工作原理

OpenEvolve遵循进化方法，包含以下组件：

![OpenEvolve Architecture](openevolve-architecture.png)

1. **提示采样器**：创建包含过去程序、其分数和问题描述的上下文丰富的提示
2. **LLM集成**：通过语言模型集成生成代码修改
3. **评估器池**：测试生成的程序并分配分数
4. **程序数据库**：存储程序及其评估指标，指导未来的进化

控制器在异步流水线中编排这些组件之间的交互，最大化吞吐量以评估尽可能多的候选解决方案。

## 开始使用

### 安装

要本地安装，请使用：
```bash
git clone https://github.com/codelion/openevolve.git
cd openevolve
pip install -e .
```

### 快速开始

我们使用OpenAI SDK，因此您可以使用任何支持OpenAI兼容API的LLM或提供商。只需设置`OPENAI_API_KEY`环境变量，
如果您使用的是OpenAI以外的提供商，请更新config.yaml中的`api_base`。对于本地模型，您可以使用
像[optillm](https://github.com/codelion/optillm)这样的推理服务器。

```python
from openevolve import OpenEvolve

# 初始化系统
evolve = OpenEvolve(
    initial_program_path="path/to/initial_program.py",
    evaluation_file="path/to/evaluator.py",
    config_path="path/to/config.yaml"
)

# 运行进化
best_program = await evolve.run(iterations=1000)
print(f"最佳程序指标:")
for name, value in best_program.metrics.items():
    print(f"  {name}: {value:.4f}")
```

### 命令行使用

OpenEvolve也可以从命令行运行：

```bash
python openevolve-run.py path/to/initial_program.py path/to/evaluator.py --config path/to/config.yaml --iterations 1000
```

### 从检查点恢复

OpenEvolve会按照`checkpoint_interval`配置参数指定的间隔自动保存检查点（默认为10次迭代）。您可以从保存的检查点恢复进化运行：

```bash
python openevolve-run.py path/to/initial_program.py path/to/evaluator.py \
  --config path/to/config.yaml \
  --checkpoint path/to/checkpoint_directory \
  --iterations 50
```

从检查点恢复时：
- 系统加载所有先前进化的程序及其指标
- 检查点编号从中断处继续（例如，如果从checkpoint_50加载，下一个检查点将是checkpoint_60）
- 保留所有进化状态（最佳程序、特征映射、档案等）
- 每个检查点目录都包含该时间点的最佳程序副本

使用检查点的示例工作流：

```bash
# 运行50次迭代（在第10、20、30、40、50次迭代时创建检查点）
python openevolve-run.py examples/function_minimization/initial_program.py \
  examples/function_minimization/evaluator.py \
  --iterations 50

# 从检查点50恢复，再运行50次迭代（在第60、70、80、90、100次迭代时创建检查点）
python openevolve-run.py examples/function_minimization/initial_program.py \
  examples/function_minimization/evaluator.py \
  --checkpoint examples/function_minimization/openevolve_output/checkpoints/checkpoint_50 \
  --iterations 50
```

### 跨检查点比较结果

每个检查点目录都包含到该时间点为止找到的最佳程序，便于比较不同时间的解决方案：

```
checkpoints/
  checkpoint_10/
    best_program.py         # 第10次迭代时的最佳程序
    best_program_info.json  # 指标和详细信息
    programs/               # 到目前为止评估的所有程序
    metadata.json           # 数据库状态
  checkpoint_20/
    best_program.py         # 第20次迭代时的最佳程序
    ...
```

您可以通过检查不同检查点的最佳程序来比较解决方案的进化：

```bash
# 比较不同检查点的最佳程序
diff -u checkpoints/checkpoint_10/best_program.py checkpoints/checkpoint_20/best_program.py

# 比较指标
cat checkpoints/checkpoint_*/best_program_info.json | grep -A 10 metrics
```

### 可视化进化树

`scripts/visualize.py`中的脚本允许您可视化进化树并在Web浏览器中显示。该脚本实时监视examples/文件夹结构中最新的检查点目录并更新图形。或者，您也可以使用`--path`参数提供特定的检查点文件夹。

```bash
# 安装依赖
pip install -r scripts/requirements.txt

# 启动可视化Web服务器并监视examples/文件夹
python scripts/visualizer.py

# 使用特定检查点启动可视化Web服务器
python scripts/visualizer.py --path examples/function_minimization/openevolve_output/checkpoints/checkpoint_100/
```

在可视化UI中，您可以：
- 在网络可视化中查看程序进化的分支，节点半径由程序适应度（=当前选择的指标）决定
- 查看节点的父子关系并在侧边栏中点击浏览（使用侧边栏中的黄色定位图标将节点居中显示在图中）
- 选择感兴趣的指标（可用的指标选择取决于您的数据集）
- 突出显示节点，例如最高分（对于选择的指标）或MAP-elites成员
- 点击节点在侧边栏中查看其代码和提示（如果检查点数据中可用）
- 在"性能"选项卡中，查看其选定指标分数与代数的图表

![OpenEvolve Visualizer](openevolve-visualizer.png)

### Docker

您也可以通过Docker安装和执行：
```bash
docker build -t openevolve .
docker run --rm -v $(pwd):/app --network="host" openevolve examples/function_minimization/initial_program.py examples/function_minimization/evaluator.py --config examples/function_minimization/config.yaml --iterations 1000
```

## 配置

OpenEvolve高度可配置。您可以在YAML文件中指定配置选项：

```yaml
# 示例配置
max_iterations: 1000
llm:
  primary_model: "gemini-2.0-flash-lite"
  secondary_model: "gemini-2.0-flash"
  temperature: 0.7
database:
  population_size: 500
  num_islands: 5
```

示例配置文件在`configs/`目录中可用：
- `default_config.yaml`：包含所有可用选项的综合配置

有关选项的完整列表，请参阅[配置指南](configs/default_config.yaml)。

## 工件通道

OpenEvolve包含一个**工件侧通道**，允许评估器捕获构建错误、性能分析结果等，以便在后续代中为LLM提供更好的反馈。此功能通过为LLM提供关于出错原因和如何修复的上下文来增强进化过程。

工件通道与传统的适应度指标并行运行。

### 示例：编译失败反馈

```python
from openevolve.evaluation_result import EvaluationResult

return EvaluationResult(
    metrics={"compile_ok": 0.0, "score": 0.0},
    artifacts={
        "stderr": "SyntaxError: invalid syntax (line 15)",
        "traceback": "...",
        "failure_stage": "compilation"
    }
)
```

下一代提示将包括：
```
## 上次执行输出
### 标准错误
```
SyntaxError: invalid syntax (line 15)
```
### 回溯
```
...
```
```

### 配置

工件可以通过配置和环境变量进行控制：

```yaml
# config.yaml
evaluator:
  enable_artifacts: true

prompt:
  include_artifacts: true
  max_artifact_bytes: 4096  # 提示中的4KB限制
  artifact_security_filter: true
```

```bash
# 禁用工件的环境变量
export ENABLE_ARTIFACTS=false
```

### 优势

- **更快收敛** - LLM可以看到出错的地方并直接修复
- **更好的错误处理** - 编译和运行时失败成为学习机会
- **丰富的调试上下文** - 完整的堆栈跟踪和错误消息指导改进
- **零开销** - 禁用时，对评估没有性能影响

## 示例

有关在各种问题上使用OpenEvolve的完整示例，请参阅`examples/`目录：

### 符号回归

一个全面的示例，演示了OpenEvolve使用LLM-SRBench基准在符号回归任务上的应用。此示例展示了OpenEvolve如何将简单的数学表达式（如线性模型）进化为准确拟合科学数据集的复杂符号公式。

[探索符号回归示例](examples/symbolic_regression/)

主要特性：
- 从基准任务自动生成初始程序
- 从简单线性模型进化为复杂数学表达式
- 在物理、化学、生物和材料科学数据集上进行评估
- 与最先进的符号回归方法相比具有竞争力的结果

### 圆形打包

我们对AlphaEvolve论文中圆形打包问题的实现。对于n=26的情况，需要在单位正方形中打包26个圆，我们也获得了SOTA结果。

[探索圆形打包示例](examples/circle_packing/)

我们成功复制了AlphaEvolve论文的结果，下面是OpenEvolve在800次迭代后找到的打包：

![alpha-evolve-replication](https://github.com/user-attachments/assets/00100f9e-2ac3-445b-9266-0398b7174193)

这正是AlphaEvolve在其论文中报告的打包（图14）：

![alpha-evolve-results](https://github.com/user-attachments/assets/0c9affa5-053d-404e-bb2d-11479ab248c9)

### 函数最小化

一个示例，展示了OpenEvolve如何将简单的随机搜索算法转换为复杂的模拟退火方法。

[探索函数最小化示例](examples/function_minimization/)

## 准备您自己的问题

要将OpenEvolve用于您自己的问题：

1. **标记代码段**：使用`# EVOLVE-BLOCK-START`和`# EVOLVE-BLOCK-END`注释标记要进化的代码段
2. **创建评估函数**：返回指标字典
3. **配置OpenEvolve**：使用适当的参数
4. **运行进化**过程

## 引用

如果您在研究中使用OpenEvolve，请引用：

```
@software{openevolve,
  title = {OpenEvolve: Open-source implementation of AlphaEvolve},
  author = {Asankhaya Sharma},
  year = {2025},
  publisher = {GitHub},
  url = {https://github.com/codelion/openevolve}
}
```
