# Configuration for function minimization example
max_iterations: 20
checkpoint_interval: 5
log_level: "INFO"

# LLM configuration
llm:
  # primary_model: "gemini-2.0-flash-lite"
  primary_model: "deepseek-ai/DeepSeek-V3-0324"
  primary_model_weight: 0.7
  # secondary_model: "gemini-2.0-flash"
  secondary_model: "deepseek-ai/DeepSeek-V3-0324"
  secondary_model_weight: 0.3
  # api_base: "https://generativelanguage.googleapis.com/v1beta/openai/"
  api_base: "https://api.sikong.shop/v1"
  api_key: "sk-7vosm99qOBxkYmk0H3CfXOy814EVDG4ZalkIo15b68ixZ6VX"
  temperature: 1
  top_p: 0.95
  max_tokens: 8192
  timeout: 120

# Prompt configuration
prompt:
  system_message: |
    Act as an expert competitive programmer and algorithm designer. Your specialty is creating highly efficient solutions for complex online problems using advanced data structures. Your task is to iteratively improve the provided C++ program to solve the problem described below.

    Problem Statement: F2. Gelly<PERSON> and <PERSON><PERSON><PERSON><PERSON> (Hard Version)

    This is the hard version of the problem. The difference between the versions is that in this version, the time limit and the constraints on n and q are higher.

    Gellyfish has an array consisting of n sets. Initially, all the sets are empty.

    Now Gellyfish will do q operations. Each operation contains one modification operation and one query operation, for the i-th (1 ≤ i ≤ q) operation:

    First, there will be a modification operation, which is one of the following:
    1. **Insert** operation: You are given an integer r. For the 1-th to r-th sets, insert element i. Note that the element inserted here is i, the index of the operation, not the index of the set.
    2. **Reverse** operation: You are given an integer r. Reverse the 1-th to r-th sets.
    3. **Delete** operation: You are given an integer x. Delete element x from all sets that contain x.

    Followed by a query operation:
    * **Query** operation: You are given an integer p. Output the smallest element in the p-th set (If the p-th set is empty, the answer is considered to be 0).

    **Additional constraint on the problem:** Gellyfish will only give the next operation after Flower has answered the previous query operation. That is, you need to solve this problem **online**.

    **Input**
    The first line contains two integers n and q (1 ≤ n, q ≤ 3 ⋅ 10⁵).
    The i-th line of the following q lines contains three integers a, b, and c (1 ≤ a, b, c ≤ n or q as specified). The operations are encoded and must be decoded using the `ans` from the previous query.
    - If a = 1 (Insert), r = (b + ans_{i−1} − 1) mod n + 1.
    - If a = 2 (Reverse), r = (b + ans_{i−1} − 1) mod n + 1.
    - If a = 3 (Delete), x = (b + ans_{i−1} − 1) mod q + 1.
    - For the query, p = (c + ans_{i−1} − 1) mod n + 1.
    (ans_0 is defined as 0).

    **Output**
    For each query operation, output the answer.
    The current program we are trying to improve is functionally incorrect.

    ### Task: Fix and Optimize the Program

    Your task is to propose modifications to the C++ code to make it correct and efficient enough to pass within the 5-second time limit for n, q up to 3e5.

    ### Expert Analysis and Improvement Hints

    The naive simulation approach in the current program is far too slow. The core challenge is designing a data structure that can efficiently handle **range insertions**, **prefix reversals**, and **global deletions** in an online setting.

    Here are key insights and directions for a successful solution:

    1.  **The `Reverse` Operation is the Key Challenge:** A standard `std::vector` reversal is O(n), which is unacceptable. This operation strongly suggests a data structure that can handle reversals lazily. **A Segment Tree with a lazy propagation flag for reversals is a very strong candidate.**
        *   **Lazy Reversal:** Each node in the segment tree should have a boolean `reversed` flag. When you push this flag down, you swap the left and right children of the node and propagate the flag to them. This avoids physically moving data.
        *   When querying for a logical index `p`, you must traverse the tree, accounting for these `reversed` flags at each level to find the correct physical leaf node.

    2.  **Handling `Insert` on a Reversible Structure:** The `Insert` operation is a range update. It needs to be compatible with the lazy reversal.
        *   In your segment tree, each node can store a data structure (e.g., a `std::set` or a sorted `std::vector`) containing the elements inserted *specifically into that node's range*.
        *   An `Insert(r)` operation becomes a range update on the segment tree for the interval `[1, r]`. Elements are added to the data stores of the nodes covering this range.

    3.  **Efficient `Query`:** To answer a query for set `p`, you need to find all elements that apply to it.
        *   This involves a path query on the segment tree. Traverse from the root to the leaf corresponding to the logical index `p`.
        *   The final contents of set `p` are the **union** of all elements stored in the nodes along this path. The smallest element is the minimum of this union.

    4.  **Managing `Delete`:** The `Delete` operation is global. Iterating through all sets is too slow.
        *   A good approach is to handle deletions lazily. Maintain a global `deleted_elements` boolean array or `std::set`.
        *   When you perform a query and find a potential smallest element, you must check if it's in the `deleted_elements` set. If it is, you must find the *next* smallest valid element. This might require iterating through the collected elements during the query.

    5.  **Putting It All Together (The likely architecture):**
        *   **Main Structure:** A Segment Tree over the indices `1...n`.
        *   **Node Data:** Each node `u` will contain:
            *   `std::set<int> elements`: A set of elements inserted into the range covered by `u`.
            *   `bool reversed`: A lazy flag for reversals.
        *   **Logic:**
            *   `Insert(r)`: Perform a range update on the segment tree for `[1, r]`, adding the element `i` to the `elements` set of the relevant nodes.
            *   `Reverse(r)`: Perform a range update on the segment tree for `[1, r]`, flipping the `reversed` lazy flag.
            *   `Delete(x)`: Add `x` to a global `deleted_elements` set.
            *   `Query(p)`: Traverse the tree to find the logical leaf for `p`, collecting all `elements` sets from the path. Find the smallest element in their union that is not in `deleted_elements`.

    Suggest a new idea to improve the code that is inspired by your expert knowledge of competitive programming and advanced data structures. Describe each change with a SEARCH/REPLACE block.

  num_top_programs: 3
  use_template_stochasticity: true

# Database configuration
database:
  population_size: 50
  archive_size: 20
  num_islands: 3
  elite_selection_ratio: 0.2
  exploitation_ratio: 0.7

# Evaluator configuration
evaluator:
  timeout: 1000   
  cascade_evaluation: false
  cascade_thresholds: [0.5, 0.75]
  parallel_evaluations: 8
  use_llm_feedback: true

# Evolution settings
diff_based_evolution: false
allow_full_rewrites: true
