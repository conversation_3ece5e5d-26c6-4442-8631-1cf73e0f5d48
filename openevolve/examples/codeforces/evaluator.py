"""
Advanced, robust evaluator for Codeforces online problems (C++ version).
Designed for AlphaEvolve to handle multiple test cases and complex interactive logic.
"""

import subprocess
import time
import os
import uuid
import hashlib
import select

# 全局缓存，避免重复评估完全相同的代码
EVALUATION_CACHE = {}

# 定义一个测试用例集，用于全面评估解决方案
# 每个测试用例都经过精心设计，以测试不同的方面
TEST_CASES = [
    {
        "name": "User Provided Test Case",
        "input_lines": [
            "5 10",
            "1 2 2",
            "2 3 1",
            "1 5 3",
            "2 2 5",
            "1 5 2",
            "2 4 4",
            "3 2 2",
            "3 1 2",
            "3 10 5",
            "3 2 4",
        ],
        "expected_output_lines": [
            "1",
            "0",
            "1",
            "1",
            "3",
            "1",
            "0",
            "5",
            "0",
            "0",
        ]
    }
]

def run_single_test(executable_path, test_case):
    """Helper function to run the program against a single test case using batch I/O."""
    start_time = time.time()

    # 将所有输入行连接成一个字符串，以匹配批量输入模式
    all_input = "\n".join(test_case["input_lines"]) + "\n"

    try:
        process = subprocess.Popen(
            [executable_path],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            universal_newlines=True
        )

        # 使用 communicate 一次性发送所有输入并获取所有输出
        # 这可以有效处理批量I/O，并设置一个总的超时时间
        stdout_data, stderr_data = process.communicate(input=all_input, timeout=15) # 15秒总超时
        execution_time = time.time() - start_time

        if process.returncode != 0:
            return {
                "status": "FAIL_RUNTIME_ERROR",
                "correct_queries": 0,
                "total_queries": len(test_case["expected_output_lines"]),
                "execution_time": execution_time,
                "details": f"Runtime error on test '{test_case['name']}'. Exit code: {process.returncode}. Stderr: {stderr_data.strip()}"
            }

        # 清理和分割输出行
        produced_outputs = [line for line in stdout_data.strip().split('\n') if line]
        expected_outputs = test_case["expected_output_lines"]

        # 检查输出行数是否匹配
        if len(produced_outputs) != len(expected_outputs):
            return {
                "status": "FAIL_WRONG_ANSWER",
                "correct_queries": 0,
                "total_queries": len(expected_outputs),
                "execution_time": execution_time,
                "details": f"Test '{test_case['name']}' produced wrong number of outputs. Got {len(produced_outputs)}, Expected {len(expected_outputs)}. Output: {produced_outputs}"
            }

        # 逐行比较输出
        correct_queries = 0
        for produced, expected in zip(produced_outputs, expected_outputs):
            if produced.strip() == expected.strip():
                correct_queries += 1

        if correct_queries == len(expected_outputs):
            status = "SUCCESS_SINGLE_TEST"
        else:
            status = "FAIL_WRONG_ANSWER"

        return {
            "status": status,
            "correct_queries": correct_queries,
            "total_queries": len(expected_outputs),
            "execution_time": execution_time,
            "details": f"Test '{test_case['name']}' finished. Got {correct_queries}/{len(expected_outputs)} correct. Outputs: {produced_outputs}"
        }

    except subprocess.TimeoutExpired:
        process.kill()
        return {
            "status": "FAIL_TIMEOUT",
            "correct_queries": 0,
            "total_queries": len(test_case["expected_output_lines"]),
            "execution_time": time.time() - start_time,
            "details": f"Execution timed out on test '{test_case['name']}'."
        }
    except Exception as e:
        process.kill()
        return {
            "status": "FAIL_EVALUATOR_ERROR",
            "correct_queries": 0,
            "total_queries": len(test_case["expected_output_lines"]),
            "execution_time": time.time() - start_time,
            "details": f"An unexpected error occurred in the evaluator: {e}"
        }


def evaluate(program_path):
    """
    Evaluates the C++ program by compiling it and running it against a suite of test cases.
    """
    try:
        with open(program_path, 'r') as f:
            cpp_code = f.read()
        if not cpp_code.strip():
            return {"final_score": -1000.0, "status": "FAIL_EMPTY_CODE", "details": "The generated code is empty."}
        code_hash = hashlib.sha256(cpp_code.encode()).hexdigest()
        if code_hash in EVALUATION_CACHE:
            return EVALUATION_CACHE[code_hash]
    except Exception as e:
        return {"final_score": -1000.0, "status": "FAIL_READ_ERROR", "details": f"Failed to read program file: {e}"}

    unique_id = uuid.uuid4()
    temp_dir = '/dev/shm' if os.path.exists('/dev/shm') else '/tmp'
    source_path = os.path.join(temp_dir, f"{unique_id}.cpp")
    executable_path = os.path.join(temp_dir, str(unique_id))

    try:
        with open(source_path, 'w') as f:
            f.write(cpp_code)
        
        compile_process = subprocess.run(
            ['g++', '-std=c++17', '-O2', '-Wfatal-errors', source_path, '-o', executable_path],
            capture_output=True, text=True, timeout=20
        )

        if compile_process.returncode != 0:
            result = {
                "final_score": -500.0,
                "status": "FAIL_COMPILE_ERROR",
                "details": f"Compilation failed: {compile_process.stderr[:1000]}"
            }
            EVALUATION_CACHE[code_hash] = result
            return result

        # --- Multi-test evaluation --- #
        total_correct_queries_all_tests = 0
        total_queries_all_tests = sum(len(tc["expected_output_lines"]) for tc in TEST_CASES)
        total_execution_time = 0
        tests_passed_count = 0
        all_details = []

        for i, test_case in enumerate(TEST_CASES):
            test_result = run_single_test(executable_path, test_case)
            
            total_execution_time += test_result["execution_time"]
            total_correct_queries_all_tests += test_result["correct_queries"]
            all_details.append(test_result["details"])

            if test_result["status"] == "SUCCESS_SINGLE_TEST":
                tests_passed_count += 1
            else:
                # 如果任何一个测试用例失败，立即中止并返回结果
                # 分数基于通过的测试数和在失败测试中答对的查询数
                final_score = tests_passed_count * 100 + test_result["correct_queries"]
                result = {
                    "final_score": float(final_score),
                    "status": test_result["status"],
                    "tests_passed": tests_passed_count,
                    "total_tests": len(TEST_CASES),
                    "correct_queries_in_failed_test": test_result["correct_queries"],
                    "total_queries_in_failed_test": test_result["total_queries"],
                    "execution_time": total_execution_time,
                    "details": "; ".join(all_details)
                }
                EVALUATION_CACHE[code_hash] = result
                return result

        # --- 如果所有测试都通过 --- #
        status = "SUCCESS"
        # 基础分10000，减去总执行时间作为惩罚
        final_score = 10000.0 - total_execution_time
        
        result = {
            "final_score": final_score,
            "status": status,
            "tests_passed": tests_passed_count,
            "total_tests": len(TEST_CASES),
            "correct_queries": total_correct_queries_all_tests,
            "total_queries": total_queries_all_tests,
            "execution_time": total_execution_time,
            "details": "All test cases passed successfully."
        }
        EVALUATION_CACHE[code_hash] = result
        return result

    finally:
        # Cleanup
        if os.path.exists(source_path):
            os.remove(source_path)
        if os.path.exists(executable_path):
            os.remove(executable_path)