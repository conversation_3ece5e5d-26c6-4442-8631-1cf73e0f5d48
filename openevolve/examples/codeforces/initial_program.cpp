// EVOLVE-BLOCK-START

#include <iostream>
#include <vector>
#include <numeric>
#include <algorithm>
#include <set>
#include <random>
#include <chrono>

using namespace std;

mt19937 rng(chrono::steady_clock::now().time_since_epoch().count());

const int INF = 2e9;

bool is_deleted[300005];

struct Node {
    Node *l, *r;
    int priority;
    int size;
    bool reverse_flag;
    set<int> elements;
    
    Node() : l(nullptr), r(nullptr), priority(uniform_int_distribution<int>()(rng)), size(1), reverse_flag(false) {}
};

Node node_pool[300005];
int node_idx = 0;

Node* get_node() {
    return &node_pool[node_idx++];
}


int get_size(Node* node) {
    return node ? node->size : 0;
}

void update_size(Node* node) {
    if (node) {
        node->size = 1 + get_size(node->l) + get_size(node->r);
    }
}

void push(Node* node) {
    if (!node || !node->reverse_flag) return;
    swap(node->l, node->r);
    if (node->l) node->l->reverse_flag ^= 1;
    if (node->r) node->r->reverse_flag ^= 1;
    node->reverse_flag = false;
}

void split(Node* t, int k, Node*& l, Node*& r) {
    if (!t) {
        l = r = nullptr;
        return;
    }
    push(t);
    int left_size = get_size(t->l);
    if (k <= left_size) {
        split(t->l, k, l, t->l);
        r = t;
    } else {
        split(t->r, k - left_size - 1, t->r, r);
        l = t;
    }
    update_size(t);
}

Node* merge(Node* l, Node* r) {
    if (!l || !r) return l ? l : r;
    push(l);
    push(r);
    if (l->priority > r->priority) {
        l->r = merge(l->r, r);
        update_size(l);
        return l;
    } else {
        r->l = merge(l, r->l);
        update_size(r);
        return r;
    }
}

Node* root = nullptr;

void query_path(Node* t, int k, int& current_min) {
     if (!t) return;
    
    while(!t->elements.empty() && is_deleted[*t->elements.begin()]) {
       t->elements.erase(t->elements.begin());
    }
    if(!t->elements.empty()) {
        current_min = min(current_min, *t->elements.begin());
    }
    
    push(t); // push reverse flag

    int left_size = get_size(t->l);
    if (k <= left_size) {
       query_path(t->l, k, current_min);
    } else if (k > left_size + 1) {
       query_path(t->r, k - left_size - 1, current_min);
    }
    // if k == left_size + 1, t is the target node, min is already updated
}


void solve() {
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    int n;
    long long q_val;
    cin >> n >> q_val;

    for (int i = 0; i < n; ++i) {
       root = merge(root, get_node());
    }
   
    long long last_ans = 0;
    for (int i = 1; i <= q_val; ++i) {
        int type;
        long long b, c_param;
        cin >> type >> b >> c_param;
        long long param_range = (type == 3) ? q_val : n;
        long long param = (b + last_ans - 1) % param_range + 1;
        long long p = (c_param + last_ans - 1) % n + 1;

        if (type == 1) {
            Node *t1, *t2;
            split(root, (int)param, t1, t2);
            if(t1) t1->elements.insert(i);
            root = merge(t1, t2);
        } else if (type == 2) {
            Node *t1, *t2;
            split(root, (int)param, t1, t2);
            if (t1) t1->reverse_flag ^= 1;
            root = merge(t1, t2);
        } else { // type 3
            if (param >= 1 && param <= q_val)
               is_deleted[param] = true;
        }
        
        int current_min = INF;
        query_path(root, p, current_min);

        last_ans = (current_min == INF) ? 0 : current_min;
        cout << last_ans << "\n";
    }
}

// EVOLVE-BLOCK-END

int main(){
    solve();
    return 0;
}
