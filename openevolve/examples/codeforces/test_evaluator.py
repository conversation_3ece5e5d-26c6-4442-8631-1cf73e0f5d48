import os
import subprocess
import pytest
import tempfile
from unittest.mock import patch, MagicMock
from evaluator import evaluate, run_single_test, TEST_CASES

# 测试用的C++代码样例
CORRECT_CPP_CODE = """
#include <iostream>
using namespace std;

int main() {
    int n, q;
    cin >> n >> q;
    
    int a[n+1] = {0};
    
    for(int i = 0; i < q; i++) {
        int type, x, y;
        cin >> type >> x >> y;
        
        if(type == 1) {
            a[x] = y;
        } else if(type == 2) {
            int count = 0;
            for(int j = 1; j <= n; j++) {
                if(a[j] == y) count++;
            }
            cout << count << endl;
        } else if(type == 3) {
            if(x <= n && a[x] == y) cout << "1" << endl;
            else cout << "0" << endl;
        }
    }
    
    return 0;
}
"""

COMPILE_ERROR_CPP_CODE = """
#include <iostream>
using namespace std;

int main() {
    syntax error here
    return 0;
}
"""

RUNTIME_ERROR_CPP_CODE = """
#include <iostream>
using namespace std;

int main() {
    int* p = nullptr;
    *p = 5;  // 空指针解引用，会导致运行时错误
    return 0;
}
"""

WRONG_ANSWER_CPP_CODE = """
#include <iostream>
using namespace std;

int main() {
    int n, q;
    cin >> n >> q;
    
    for(int i = 0; i < q; i++) {
        int type, x, y;
        cin >> type >> x >> y;
        cout << "0" << endl;  // 总是输出错误答案
    }
    
    return 0;
}
"""

@pytest.fixture
def temp_cpp_file():
    """创建临时C++文件的fixture"""
    with tempfile.NamedTemporaryFile(suffix='.cpp', delete=False) as f:
        yield f.name
    # 测试后清理
    if os.path.exists(f.name):
        os.remove(f.name)

def test_evaluate_correct_code(temp_cpp_file):
    """测试评估正确的代码"""
    with open(temp_cpp_file, 'w') as f:
        f.write(CORRECT_CPP_CODE)
    
    # 模拟编译和执行过程
    with patch('subprocess.run') as mock_run, \
         patch('subprocess.Popen') as mock_popen:
        
        # 模拟编译成功
        mock_run.return_value = MagicMock(returncode=0)
        
        # 模拟程序执行并返回正确输出
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (
            "\n".join(TEST_CASES[0]["expected_output_lines"]), ""
        )
        mock_popen.return_value = mock_process
        
        result = evaluate(temp_cpp_file)
        
        assert result["status"] == "SUCCESS"
        assert result["tests_passed"] == 1
        assert result["total_tests"] == 1
        assert result["correct_queries"] == len(TEST_CASES[0]["expected_output_lines"])

def test_evaluate_compile_error(temp_cpp_file):
    """测试评估编译错误的代码"""
    with open(temp_cpp_file, 'w') as f:
        f.write(COMPILE_ERROR_CPP_CODE)
    
    # 模拟编译失败
    with patch('subprocess.run') as mock_run:
        mock_run.return_value = MagicMock(
            returncode=1, 
            stderr="error: syntax error"
        )
        
        result = evaluate(temp_cpp_file)
        
        assert result["status"] == "FAIL_COMPILE_ERROR"
        assert result["final_score"] == -500.0

def test_evaluate_runtime_error(temp_cpp_file):
    """测试评估运行时错误的代码"""
    with open(temp_cpp_file, 'w') as f:
        f.write(RUNTIME_ERROR_CPP_CODE)
    
    # 模拟编译成功但运行失败
    with patch('subprocess.run') as mock_run, \
         patch('subprocess.Popen') as mock_popen:
        
        mock_run.return_value = MagicMock(returncode=0)
        
        mock_process = MagicMock()
        mock_process.returncode = 1  # 非零返回码表示运行时错误
        mock_process.communicate.return_value = ("", "Segmentation fault")
        mock_popen.return_value = mock_process
        
        result = evaluate(temp_cpp_file)
        
        assert result["status"] == "FAIL_RUNTIME_ERROR"
        assert result["tests_passed"] == 0

def test_evaluate_wrong_answer(temp_cpp_file):
    """测试评估答案错误的代码"""
    with open(temp_cpp_file, 'w') as f:
        f.write(WRONG_ANSWER_CPP_CODE)
    
    # 模拟编译成功但答案错误
    with patch('subprocess.run') as mock_run, \
         patch('subprocess.Popen') as mock_popen:
        
        mock_run.return_value = MagicMock(returncode=0)
        
        mock_process = MagicMock()
        mock_process.returncode = 0
        # 所有输出都是0，与预期不符
        mock_process.communicate.return_value = (
            "0\n" * len(TEST_CASES[0]["expected_output_lines"]), ""
        )
        mock_popen.return_value = mock_process
        
        result = evaluate(temp_cpp_file)
        
        assert result["status"] == "FAIL_WRONG_ANSWER"
        assert result["tests_passed"] == 0

def test_run_single_test_timeout():
    """测试程序执行超时的情况"""
    with patch('subprocess.Popen') as mock_popen:
        mock_process = MagicMock()
        # 模拟超时异常
        mock_process.communicate.side_effect = subprocess.TimeoutExpired(cmd="test", timeout=15)
        mock_popen.return_value = mock_process
        
        result = run_single_test("dummy_path", TEST_CASES[0])
        
        assert result["status"] == "FAIL_TIMEOUT"