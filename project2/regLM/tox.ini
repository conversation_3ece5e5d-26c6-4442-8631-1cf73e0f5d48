# Tox configuration file
# Read more under https://tox.wiki/
# THIS SCRIPT IS SUPPOSED TO BE AN EXAMPLE. MODIFY IT ACCORDING TO YOUR NEEDS!

[tox]
minversion = 3.24
envlist = default
isolated_build = True


[testenv]
description = Invoke pytest to run automated tests
setenv =
    TOXINIDIR = {toxinidir}
passenv =
    HOME
    SETUPTOOLS_*
extras =
    testing
commands =
    pytest {posargs}


# # To run `tox -e lint` you need to make sure you have a
# # `.pre-commit-config.yaml` file. See https://pre-commit.com
# [testenv:lint]
# description = Perform static analysis and style checks
# skip_install = True
# deps = pre-commit
# passenv =
#     HOMEPATH
#     PROGRAMDATA
#     SETUPTOOLS_*
# commands =
#     pre-commit run --all-files {posargs:--show-diff-on-failure}


[testenv:{build,clean}]
description =
    build: Build the package in isolation according to PEP517, see https://github.com/pypa/build
    clean: Remove old distribution files and temporary build artifacts (./build and ./dist)
# https://setuptools.pypa.io/en/stable/build_meta.html#how-to-use-it
skip_install = True
changedir = {toxinidir}
deps =
    build: build[virtualenv]
passenv =
    SETUPTOOLS_*
commands =
    clean: python -c 'import shutil; [shutil.rmtree(p, True) for p in ("build", "dist", "docs/_build")]'
    clean: python -c 'import pathlib, shutil; [shutil.rmtree(p, True) for p in pathlib.Path("src").glob("*.egg-info")]'
    build: python -m build {posargs}
# By default, both `sdist` and `wheel` are built. If your sdist is too big or you don't want
# to make it available, consider running: `tox -e build -- --wheel`


[testenv:{docs,doctests,linkcheck}]
description =
    docs: Invoke sphinx-build to build the docs
    doctests: Invoke sphinx-build to run doctests
    linkcheck: Check for broken links in the documentation
passenv =
    SETUPTOOLS_*
setenv =
    DOCSDIR = {toxinidir}/docs
    BUILDDIR = {toxinidir}/docs/_build
    docs: BUILD = html
    doctests: BUILD = doctest
    linkcheck: BUILD = linkcheck
deps =
    -r {toxinidir}/docs/requirements.txt
    # ^  requirements.txt shared with Read The Docs
commands =
    sphinx-build --color -b {env:BUILD} -d "{env:BUILDDIR}/doctrees" "{env:DOCSDIR}" "{env:BUILDDIR}/{env:BUILD}" {posargs}


[testenv:publish]
description =
    Publish the package you have been developing to a package index server.
    By default, it uses testpypi. If you really want to publish your package
    to be publicly accessible in PyPI, use the `-- --repository pypi` option.
skip_install = True
changedir = {toxinidir}
passenv =
    # See: https://twine.readthedocs.io/en/latest/
    TWINE_USERNAME
    TWINE_PASSWORD
    TWINE_REPOSITORY
    TWINE_REPOSITORY_URL
deps = twine
commands =
    python -m twine check dist/*
    python -m twine upload {posargs:--repository {env:TWINE_REPOSITORY:testpypi}} dist/*
