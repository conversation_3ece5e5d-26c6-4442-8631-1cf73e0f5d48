{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c0ebfa2f-1038-416c-8af2-084b5b9338c5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.8/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import torch\n", "from plotnine import *"]}, {"cell_type": "markdown", "id": "d34228ac-864d-4bdb-8c98-ccca03651b79", "metadata": {}, "source": ["## Download yeast native promoter expression data"]}, {"cell_type": "code", "execution_count": 2, "id": "eb7ef50b-275d-426e-8060-ff37f4deed98", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sequence</th>\n", "      <th>expression</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAACAAAGAAAAAGG...</td>\n", "      <td>13.168816</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAACAAAAAGG...</td>\n", "      <td>13.457919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGA...</td>\n", "      <td>13.855758</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...</td>\n", "      <td>12.043120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...</td>\n", "      <td>13.529583</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                Sequence  expression\n", "index                                                               \n", "0      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAACAAAGAAAAAGG...   13.168816\n", "1      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAACAAAAAGG...   13.457919\n", "2      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGA...   13.855758\n", "3      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...   12.043120\n", "4      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...   13.529583"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["yeast_data = pd.read_csv(\n", "    'https://zenodo.org/records/4436477/files/Native_complex.csv', usecols=(0, 1, 2),\n", "    skiprows=1,\n", "    index_col=0,\n", "    names=['index', 'Sequence', 'expression'])\n", "\n", "yeast_data.head()"]}, {"cell_type": "markdown", "id": "e6910101-20e8-44a1-af06-60c2a6254d5d", "metadata": {}, "source": ["## Bin the sequences and create labels"]}, {"cell_type": "code", "execution_count": 3, "id": "5aac4632-16a7-437e-88fd-3cd750672637", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["expression [9.24094738948754]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sequence</th>\n", "      <th>expression</th>\n", "      <th>label</th>\n", "      <th>expression_token</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAACAAAGAAAAAGG...</td>\n", "      <td>13.168816</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAACAAAAAGG...</td>\n", "      <td>13.457919</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGA...</td>\n", "      <td>13.855758</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...</td>\n", "      <td>12.043120</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...</td>\n", "      <td>13.529583</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                Sequence  expression label  \\\n", "index                                                                        \n", "0      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAACAAAGAAAAAGG...   13.168816     1   \n", "1      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAACAAAAAGG...   13.457919     1   \n", "2      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGA...   13.855758     1   \n", "3      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...   12.043120     1   \n", "4      TGCATTTTTTTCACATCAAAAAAAAAAAGAAAAAGAAAGAAAAAGG...   13.529583     1   \n", "\n", "      expression_token  \n", "index                   \n", "0                    1  \n", "1                    1  \n", "2                    1  \n", "3                    1  \n", "4                    1  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import reglm.utils\n", "yeast_data = reglm.utils.tokenize(yeast_data, cols=['expression'],\n", "                             names=['expression'], n_bins=2)\n", "\n", "yeast_data.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "b2f3b792-ce08-49a7-be9f-22ff5c3e6a48", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 400x200 with 1 Axes>"]}, "metadata": {"image/png": {"height": 200, "width": 400}}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure Size: (400 x 200)>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["(\n", "    ggplot(yeast_data, aes(x='expression', color='label')) + geom_density()\n", "    + theme(figure_size=(4,2))\n", ")"]}, {"cell_type": "markdown", "id": "c8e7a70e-c8ac-462d-bafb-85ea81b7b248", "metadata": {}, "source": ["## Split data into train, val and test"]}, {"cell_type": "code", "execution_count": 5, "id": "4e64655e-1939-4464-bbd4-2f234a075cc4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2929, 500, 500)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["rng = np.random.RandomState(0)\n", "train = yeast_data[['Sequence', 'label']].copy()\n", "\n", "val = train.sample(500, random_state=rng)\n", "train = train.loc[~train.index.isin(val.index)]\n", "\n", "test = train.sample(500, random_state=rng)\n", "train = train.loc[~train.index.isin(test.index)]\n", "\n", "len(train), len(val), len(test)"]}, {"cell_type": "markdown", "id": "7d540698-ac03-43bd-8ed3-c9313c49f508", "metadata": {}, "source": ["## Create torch datasets for training and validation"]}, {"cell_type": "code", "execution_count": 6, "id": "ec376e22-1eeb-472c-a06e-017cdfdace80", "metadata": {}, "outputs": [], "source": ["import reglm.dataset\n", "train_ds = reglm.dataset.CharDataset(\n", "    train.Sequence.tolist(), labels = train.label.tolist())\n", "val_ds = reglm.dataset.CharDataset(\n", "    seqs = val.Sequence.tolist(), labels = val.label.tolist())"]}, {"cell_type": "markdown", "id": "9c35f1f1-b769-442d-964b-346288eb4af5", "metadata": {}, "source": ["## Build the regLM model"]}, {"cell_type": "code", "execution_count": 7, "id": "cf3353f4-1244-4b0c-a3d1-f5cfaa03fbd8", "metadata": {}, "outputs": [], "source": ["import reglm.lightning"]}, {"cell_type": "code", "execution_count": 10, "id": "7f8f6d36-deee-4bc2-a0cd-fc0b95309420", "metadata": {}, "outputs": [], "source": ["config = {\n", " 'd_model': 32,\n", " 'n_layer': 2,\n", " 'd_inner': 32,\n", " 'vocab_size': 12,\n", " 'pad_vocab_size_multiple': 8,\n", " 'return_hidden_state': True,\n", " 'layer': {\n", "     'emb_dim': 5,\n", "     'filter_order': 64,\n", "     'l_max': train_ds.seq_len + train_ds.label_len + 1,\n", "     '_name_': 'hyena'\n", " }\n", "}"]}, {"cell_type": "code", "execution_count": 12, "id": "9f8b9672-8c5f-4268-81b0-e3a4a836ff91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["number of parameters: 0.04M\n"]}], "source": ["model = reglm.lightning.LightningModel(\n", "    config=config, lr=1e-3, label_len=1).to(torch.device(0))"]}, {"cell_type": "markdown", "id": "a517b7ba-3a36-42ae-968c-4aa4b8fb0ff6", "metadata": {}, "source": ["## Train and validate model"]}, {"cell_type": "code", "execution_count": 13, "id": "bb35ab53-cb7b-4e01-af92-bb38897d453a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "IPU available: False, using: 0 IPUs\n", "HPU available: False, using: 0 HPUs\n", "LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3,4,5,6,7]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Validation DataLoader 0: 100%|██████████████████████████████████████| 2/2 [00:00<00:00,  2.59it/s]\n", "Val loss: 2.639369010925293, val acc: 0.3059999942779541\n", "Validation DataLoader 0: 100%|██████████████████████████████████████| 2/2 [00:00<00:00,  2.56it/s]\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\">      Validate metric      </span>┃<span style=\"font-weight: bold\">       DataLoader 0        </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│<span style=\"color: #008080; text-decoration-color: #008080\">          val_acc          </span>│<span style=\"color: #800080; text-decoration-color: #800080\">    0.3059999942779541     </span>│\n", "│<span style=\"color: #008080; text-decoration-color: #008080\">         val_loss          </span>│<span style=\"color: #800080; text-decoration-color: #800080\">    2.6393609046936035     </span>│\n", "└───────────────────────────┴───────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1m     Validate metric     \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      DataLoader 0       \u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│\u001b[36m \u001b[0m\u001b[36m         val_acc         \u001b[0m\u001b[36m \u001b[0m│\u001b[35m \u001b[0m\u001b[35m   0.3059999942779541    \u001b[0m\u001b[35m \u001b[0m│\n", "│\u001b[36m \u001b[0m\u001b[36m        val_loss         \u001b[0m\u001b[36m \u001b[0m│\u001b[35m \u001b[0m\u001b[35m   2.6393609046936035    \u001b[0m\u001b[35m \u001b[0m│\n", "└───────────────────────────┴───────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3,4,5,6,7]\n", "\n", "  | Name      | Type               | Params\n", "-------------------------------------------------\n", "0 | model     | ConvLMHeadModel    | 37.1 K\n", "1 | train_acc | MulticlassAccuracy | 0     \n", "2 | val_acc   | MulticlassAccuracy | 0     \n", "-------------------------------------------------\n", "37.1 K    Trainable params\n", "0         Non-trainable params\n", "37.1 K    Total params\n", "0.148     Total estimated model params size (MB)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Sanity Checking DataLoader 0: 100%|█████████████████████████████████| 2/2 [00:00<00:00, 60.27it/s]\n", "Val loss: 2.639369010925293, val acc: 0.3059999942779541\n", "                                                                                                  "]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.8/site-packages/pytorch_lightning/trainer/trainer.py:1595: PossibleUserWarning: The number of training batches (12) is smaller than the logging interval Trainer(log_every_n_steps=50). Set a lower value for log_every_n_steps if you want to see logs for the training epoch.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 0:  71%|█████▋  | 10/14 [00:02<00:01,  3.79it/s, loss=2.41, v_num=10, train_loss_step=2.230]\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 0:  79%|██████▎ | 11/14 [00:04<00:01,  2.43it/s, loss=2.41, v_num=10, train_loss_step=2.230]\u001b[A\n", "Epoch 0:  86%|██████▊ | 12/14 [00:04<00:00,  2.64it/s, loss=2.41, v_num=10, train_loss_step=2.230]\u001b[A\n", "Val loss: 2.1948914527893066, val acc: 0.31873273849487305\n", "Epoch 0:  86%|██████▊ | 12/14 [00:04<00:00,  2.64it/s, loss=2.41, v_num=10, train_loss_step=2.230]\n", "Epoch 1:  71%|▋| 10/14 [00:02<00:01,  3.96it/s, loss=2.16, v_num=10, train_loss_step=1.870, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 1:  79%|▊| 11/14 [00:04<00:01,  2.38it/s, loss=2.16, v_num=10, train_loss_step=1.870, train_\u001b[A\n", "Epoch 1:  86%|▊| 12/14 [00:04<00:00,  2.60it/s, loss=2.16, v_num=10, train_loss_step=1.870, train_\u001b[A\n", "Val loss: 1.8451852798461914, val acc: 0.3271621763706207\n", "Epoch 1:  86%|▊| 12/14 [00:04<00:00,  2.59it/s, loss=2.16, v_num=10, train_loss_step=1.870, train_\n", "Epoch 2:  71%|▋| 10/14 [00:02<00:00,  4.32it/s, loss=1.83, v_num=10, train_loss_step=1.640, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 2:  79%|▊| 11/14 [00:04<00:01,  2.65it/s, loss=1.83, v_num=10, train_loss_step=1.640, train_\u001b[A\n", "Epoch 2:  86%|▊| 12/14 [00:04<00:00,  2.88it/s, loss=1.83, v_num=10, train_loss_step=1.640, train_\u001b[A\n", "Val loss: 1.6297390460968018, val acc: 0.33221980929374695\n", "Epoch 2:  86%|▊| 12/14 [00:04<00:00,  2.88it/s, loss=1.83, v_num=10, train_loss_step=1.640, train_\n", "Epoch 3:  71%|▋| 10/14 [00:02<00:01,  3.77it/s, loss=1.62, v_num=10, train_loss_step=1.520, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 3:  79%|▊| 11/14 [00:04<00:01,  2.23it/s, loss=1.62, v_num=10, train_loss_step=1.520, train_\u001b[A\n", "Epoch 3:  86%|▊| 12/14 [00:04<00:00,  2.43it/s, loss=1.62, v_num=10, train_loss_step=1.520, train_\u001b[A\n", "Val loss: 1.5154331922531128, val acc: 0.335591584444046\n", "Epoch 3:  86%|▊| 12/14 [00:04<00:00,  2.43it/s, loss=1.62, v_num=10, train_loss_step=1.520, train_\n", "Epoch 4:  71%|▋| 10/14 [00:02<00:01,  3.97it/s, loss=1.51, v_num=10, train_loss_step=1.460, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 4:  79%|▊| 11/14 [00:04<00:01,  2.47it/s, loss=1.51, v_num=10, train_loss_step=1.460, train_\u001b[A\n", "Epoch 4:  86%|▊| 12/14 [00:04<00:00,  2.70it/s, loss=1.51, v_num=10, train_loss_step=1.460, train_\u001b[A\n", "Val loss: 1.4504921436309814, val acc: 0.3395238220691681\n", "Epoch 4:  86%|▊| 12/14 [00:04<00:00,  2.69it/s, loss=1.51, v_num=10, train_loss_step=1.460, train_\n", "Epoch 5:  71%|▋| 10/14 [00:02<00:00,  4.12it/s, loss=1.45, v_num=10, train_loss_step=1.410, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 5:  79%|▊| 11/14 [00:04<00:01,  2.33it/s, loss=1.45, v_num=10, train_loss_step=1.410, train_\u001b[A\n", "Epoch 5:  86%|▊| 12/14 [00:04<00:00,  2.53it/s, loss=1.45, v_num=10, train_loss_step=1.410, train_\u001b[A\n", "Val loss: 1.3980190753936768, val acc: 0.3459864854812622\n", "Epoch 5:  86%|▊| 12/14 [00:04<00:00,  2.53it/s, loss=1.45, v_num=10, train_loss_step=1.410, train_\n", "Epoch 6:  71%|▋| 10/14 [00:02<00:00,  4.17it/s, loss=1.4, v_num=10, train_loss_step=1.360, train_l\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 6:  79%|▊| 11/14 [00:04<00:01,  2.42it/s, loss=1.4, v_num=10, train_loss_step=1.360, train_l\u001b[A\n", "Epoch 6:  86%|▊| 12/14 [00:04<00:00,  2.64it/s, loss=1.4, v_num=10, train_loss_step=1.360, train_l\u001b[A\n", "Val loss: 1.3507966995239258, val acc: 0.3539579510688782\n", "Epoch 6:  86%|▊| 12/14 [00:04<00:00,  2.63it/s, loss=1.4, v_num=10, train_loss_step=1.360, train_l\n", "Epoch 7:  71%|▋| 10/14 [00:02<00:00,  4.27it/s, loss=1.35, v_num=10, train_loss_step=1.320, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 7:  79%|▊| 11/14 [00:04<00:01,  2.45it/s, loss=1.35, v_num=10, train_loss_step=1.320, train_\u001b[A\n", "Epoch 7:  86%|▊| 12/14 [00:04<00:00,  2.67it/s, loss=1.35, v_num=10, train_loss_step=1.320, train_\u001b[A\n", "Val loss: 1.3169329166412354, val acc: 0.3638738691806793\n", "Epoch 7:  86%|▊| 12/14 [00:04<00:00,  2.67it/s, loss=1.35, v_num=10, train_loss_step=1.320, train_\n", "Epoch 8:  71%|▋| 10/14 [00:02<00:01,  3.98it/s, loss=1.31, v_num=10, train_loss_step=1.280, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 8:  79%|▊| 11/14 [00:04<00:01,  2.31it/s, loss=1.31, v_num=10, train_loss_step=1.280, train_\u001b[A\n", "Epoch 8:  86%|▊| 12/14 [00:04<00:00,  2.52it/s, loss=1.31, v_num=10, train_loss_step=1.280, train_\u001b[A\n", "Val loss: 1.2752059698104858, val acc: 0.37444552779197693\n", "Epoch 8:  86%|▊| 12/14 [00:04<00:00,  2.51it/s, loss=1.31, v_num=10, train_loss_step=1.280, train_\n", "Epoch 9:  71%|▋| 10/14 [00:02<00:01,  3.87it/s, loss=1.27, v_num=10, train_loss_step=1.250, train_\u001b[A\n", "Validation: 0it [00:00, ?it/s]\u001b[A\n", "Validation:   0%|                                                           | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Validation DataLoader 0:   0%|                                              | 0/2 [00:00<?, ?it/s]\u001b[A\n", "Epoch 9:  79%|▊| 11/14 [00:04<00:01,  2.48it/s, loss=1.27, v_num=10, train_loss_step=1.250, train_\u001b[A\n", "Epoch 9:  86%|▊| 12/14 [00:04<00:00,  2.70it/s, loss=1.27, v_num=10, train_loss_step=1.250, train_\u001b[A\n", "Val loss: 1.238427758216858, val acc: 0.3836981952190399\n", "Epoch 9:  86%|▊| 12/14 [00:04<00:00,  2.69it/s, loss=1.27, v_num=10, train_loss_step=1.250, train_\n", "Epoch 9: 100%|█| 14/14 [00:04<00:00,  3.11it/s, loss=1.27, v_num=10, train_loss_step=1.240, train_\u001b[A"]}, {"name": "stderr", "output_type": "stream", "text": ["`Trainer.fit` stopped: `max_epochs=10` reached.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9: 100%|█| 14/14 [00:04<00:00,  3.11it/s, loss=1.27, v_num=10, train_loss_step=1.240, train_\n"]}], "source": ["trainer = model.train_on_dataset(train_ds, val_ds, max_epochs=10,\n", "            batch_size=256, num_workers=8, device=0, val_check_interval=10\n", "        )"]}, {"cell_type": "code", "execution_count": 14, "id": "e2d15100-1b34-4229-9dcf-229cf5c1e0ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["'./lightning_logs/version_10/checkpoints/epoch=9-step=118.ckpt'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["trainer.checkpoint_callback.best_model_path"]}, {"cell_type": "markdown", "id": "ac1a09a2-9c03-4d18-aca9-2575ab479a62", "metadata": {}, "source": ["## Load best validation model"]}, {"cell_type": "code", "execution_count": 15, "id": "061ad00c-f3f4-4786-a056-3819676ec06b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["number of parameters: 0.04M\n"]}], "source": ["model = reglm.lightning.LightningModel.load_from_checkpoint(\n", "    trainer.checkpoint_callback.best_model_path)\n", "\n", "model = model.to(torch.device(0))"]}, {"cell_type": "markdown", "id": "ae4424cd-ca34-4274-ba20-9d16c8894adb", "metadata": {}, "source": ["## Compute accuracy on the test set"]}, {"cell_type": "code", "execution_count": 16, "id": "125dded2-cde5-4013-9c5b-d3fde2bdf17e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean accuracy: 0.481\n"]}], "source": ["import reglm.metrics\n", "test = reglm.metrics.compute_accuracy(model, test)"]}, {"cell_type": "code", "execution_count": 17, "id": "f0a6e7f4-0cda-4959-a6a1-e4bb3ae9bc3e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sequence</th>\n", "      <th>label</th>\n", "      <th>acc</th>\n", "      <th>acc_mean</th>\n", "    </tr>\n", "    <tr>\n", "      <th>index</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1119</th>\n", "      <td>TGCATTTTTTTCACATCATTTTCATAACCATTTACCCTCATAAATG...</td>\n", "      <td>0</td>\n", "      <td>[True, True, True, True, True, True, True, Tru...</td>\n", "      <td>0.567568</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1814</th>\n", "      <td>TGCATTTTTTTCACATCCTAATTGTGACATAGGAACGAAAATTAAT...</td>\n", "      <td>0</td>\n", "      <td>[True, True, True, True, True, True, True, Tru...</td>\n", "      <td>0.468468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1539</th>\n", "      <td>TGCATTTTTTTCACATCCCTGCGTGCCCGCGCACCCTGGCGACTTC...</td>\n", "      <td>1</td>\n", "      <td>[True, True, True, True, True, True, True, Tru...</td>\n", "      <td>0.405405</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                Sequence label  \\\n", "index                                                            \n", "1119   TGCATTTTTTTCACATCATTTTCATAACCATTTACCCTCATAAATG...     0   \n", "1814   TGCATTTTTTTCACATCCTAATTGTGACATAGGAACGAAAATTAAT...     0   \n", "1539   TGCATTTTTTTCACATCCCTGCGTGCCCGCGCACCCTGGCGACTTC...     1   \n", "\n", "                                                     acc  acc_mean  \n", "index                                                               \n", "1119   [True, True, True, True, True, True, True, Tru...  0.567568  \n", "1814   [True, True, True, True, True, True, True, Tru...  0.468468  \n", "1539   [True, True, True, True, True, True, True, Tru...  0.405405  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["test.head(3)"]}, {"cell_type": "code", "execution_count": 18, "id": "5bce165e-51e0-4084-ae4c-f4a12cd69a99", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 300x300 with 1 Axes>"]}, "metadata": {"image/png": {"height": 300, "width": 300}}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure Size: (300 x 300)>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["(\n", "    ggplot(test, aes(x='label', y='acc_mean')) + geom_boxplot()\n", "    + theme(figure_size=(3, 3)) + ylab('Mean Accuracy')\n", ")"]}, {"cell_type": "markdown", "id": "83e564cb-6f2e-4f16-9b1e-fd927fe05c72", "metadata": {}, "source": ["## Generate new sequences"]}, {"cell_type": "code", "execution_count": 19, "id": "95f3656c-f34e-4542-9c3b-44fc79f3e024", "metadata": {}, "outputs": [], "source": ["prompts = [\"0\", \"1\"]"]}, {"cell_type": "code", "execution_count": 21, "id": "5cd36f3d-b830-4c23-93c6-012e9e031e6a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['TGCTATATTTTTTCACATCAACTACATAATAATAAATTTTAATAAAAAAATTAAGAAATATATATAATATATAAGGTATGGTTAATGTTTACGATTATTTATGTTTTCTT',\n", " 'AGATATAATTTTAACATAACAAAATAAAAAAATTTAAGGTAAAATATATTTTTTAAAATAAATAAAAAATTTTAAATAAATAAAAATAATAAAAATTATAAAATTAAAGG']"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["model.generate(prompts, max_new_tokens=110, top_k=2)"]}, {"cell_type": "code", "execution_count": null, "id": "e0c4a299-083d-4c69-8ae2-78df39a650be", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.17"}}, "nbformat": 4, "nbformat_minor": 5}