[metadata]
name = regLM
description = Add a short description here!
author = Genentech, Inc.
author_email = <EMAIL>
license = MIT
license_files = LICENSE.txt
long_description = file: README.md
long_description_content_type = text/markdown; charset=UTF-8; variant=GFM
url = https://https://github.com/Genentech/regLM

# Add here related links, for example:
project_urls =
    Documentation = https://genentech.github.io/regLM/
    Source = https://github.com/Genentech/regLM

# Change if running only on Windows, Mac or Linux (comma-separated)
platforms = any

# Add here all kinds of additional classifiers as defined under
# https://pypi.org/classifiers/
classifiers =
    Development Status :: 4 - Beta
    Programming Language :: Python


[options]
zip_safe = False
packages = find_namespace:
include_package_data = True
package_dir =
    =src

# Require a min/specific Python version (comma-separated conditions)
python_requires = >=3.8

# Add here dependencies of your project (line-separated), e.g. requests>=2.2,<3.0.
# Version specifiers like >=2.2,<3.0 avoid problems due to API changes in
# new major versions.
install_requires =
    numpy
    pandas
    torch < 2.0
    torchmetrics
    pytorch-lightning < 2.0
    bpnet-lite < 0.6.0
    captum == 0.5.0
    enformer_pytorch < 0.8.7


[options.packages.find]
where = src
exclude =
    tests

[options.extras_require]
# Add here additional requirements for extra features, to install with:
# `pip install reglm[PDF]` like:
# PDF = ReportLab; RXP

# Add here test requirements (semicolon/line-separated)
testing =
    setuptools
    pytest
    pytest-cov

[options.entry_points]

[tool:pytest]
addopts =
    --cov reglm --cov-report term-missing
    --verbose
norecursedirs =
    dist
    build
    .tox
testpaths = tests

[devpi:upload]
no_vcs = 1
formats = bdist_wheel

[flake8]
# Some sane defaults for the code style checker flake8
max_line_length = 88
extend_ignore = E203, W503
# ^  Black-compatible
#    E203 and W503 have edge cases handled by black
exclude =
    .tox
    build
    dist
    .eggs
    docs/conf.py

[isort]
profile = black

[pyscaffold]
# PyScaffold's parameters when the project was created.
# This will be used when updating. Do not change!
version = 4.5
package = reglm
extensions =
    github_actions
    markdown
    pre_commit
