repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.5.0
  hooks:
  - id: trailing-whitespace
  - id: check-added-large-files
  - id: check-ast
  - id: check-json
  - id: check-merge-conflict
  - id: check-xml
  - id: check-yaml
  - id: debug-statements
  - id: end-of-file-fixer
  - id: requirements-txt-fixer
  - id: mixed-line-ending
    args: ['--fix=auto']  # replace 'auto' with 'lf' to enforce Linux/Mac line endings or 'crlf' for Windows

- repo: https://github.com/PyCQA/isort
  rev: 5.12.0
  hooks:
  - id: isort

- repo: https://github.com/psf/black
  rev: 23.11.0
  hooks:
  - id: black
    language_version: python3

- repo: https://github.com/PyCQA/flake8
  rev: 6.1.0
  hooks:
  - id: flake8
