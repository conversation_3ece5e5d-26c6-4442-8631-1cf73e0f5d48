name: tests

on:
  push:
    branches: [main]
    tags: ['v[0-9]*', '[0-9]+.[0-9]+*']  # Match tags that resemble a version
  pull_request:  # Run in every PR
  workflow_dispatch:  # Allow manually triggering the workflow

permissions:
  contents: write

concurrency:
  group: >-
    ${{ github.workflow }}-${{ github.ref_type }}-
    ${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      wheel-distribution: ${{ steps.wheel-distribution.outputs.path }}
    steps:
      - uses: actions/checkout@v3
        with: {fetch-depth: 0}  # deep clone for setuptools-scm
      - uses: actions/setup-python@v4
        id: setup-python
        with: {python-version: "3.11"}
      - name: Run static analysis and format checkers
        run: pipx run pre-commit run --all-files --show-diff-on-failure
      - name: Build package distribution files
        run: >-
          pipx run --python '${{ steps.setup-python.outputs.python-path }}'
          tox -e clean,build
      - name: Record the path of wheel distribution
        id: wheel-distribution
        run: echo "path=$(ls dist/*.whl)" >> $GITHUB_OUTPUT
      - name: Store the distribution files for use in other stages
        # `tests` and `publish` will use the same pre-built distributions,
        # so we make sure to release the exact same package that was tested
        uses: actions/upload-artifact@v3
        with:
          name: python-distribution-files
          path: dist/
          retention-days: 1

  test:
    needs: prepare
    strategy:
      matrix:
        python:
        - "3.8"  # oldest Python supported by PSF
        - "3.11"  # newest Python that is stable
        platform:
        - ubuntu-latest
          # - macos-latest
          # - windows-latest
    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        id: setup-python
        with:
          python-version: ${{ matrix.python }}
      - name: Retrieve pre-built distribution files
        uses: actions/download-artifact@v3
        with: {name: python-distribution-files, path: dist/}
      - name: Run tests
        run: >-
          pipx run --python '${{ steps.setup-python.outputs.python-path }}'
          tox --installpkg '${{ needs.prepare.outputs.wheel-distribution }}'
          -- -rFEx --durations 10 --color yes  # pytest args
      - name: Generate coverage report
        run: pipx run coverage lcov -o coverage.lcov
      - name: Upload partial coverage report
        uses: coverallsapp/github-action@master
        with:
          path-to-lcov: coverage.lcov
          github-token: ${{ secrets.GITHUB_TOKEN }}
          flag-name: ${{ matrix.platform }} - py${{ matrix.python }}
          parallel: true

  finalize:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Finalize coverage report
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          parallel-finished: true

  docs:
    name: Push Sphinx Pages
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: >-
          pip install --upgrade pip
          pip install Cython wheel
          pip install -r docs/requirements.txt
      - name: Sphinx build
        run: |
          sphinx-build docs _build
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          publish_branch: gh-pages
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: _build/
          force_orphan: true
#  publish:
#    needs: finalize
#    if: ${{ github.event_name == 'push' && contains(github.ref, 'refs/tags/') }}
#    runs-on: ubuntu-latest
#    permissions:
#      contents: write
#    steps:
#      - uses: actions/checkout@v3
#      - uses: actions/setup-python@v4
#        with: {python-version: "3.11"}
#      - name: Retrieve pre-built distribution files
#        uses: actions/download-artifact@v3
#        with: {name: python-distribution-files, path: dist/}
#      - name: Publish Package
#        env:
          # TODO: Set your PYPI_TOKEN as a secret using GitHub UI
          # - https://pypi.org/help/#apitoken
          # - https://docs.github.com/en/actions/security-guides/encrypted-secrets
          # TWINE_REPOSITORY: pypi
          # TWINE_USERNAME: __token__
          # TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
          #        run: pipx run tox -e publish
