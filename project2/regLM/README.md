
# regLM
regLM is a toolkit for training hyenaDNA-based autoregressive language models on DNA sequences and generating novel regulatory elements.

![regLM schematic](fig1.png)

## Documentation

[Documentation](https://genentech.github.io/regLM)

## Tutorials

[Tutorials](tutorials)

## Installation

### 1. Install HyenaDNA
To use regLM, first install HyenaDNA from GitHub following the instructions: https://github.com/HazyResearch/hyena-dna

### 2. Install regLM
```
git clone https://github.com/Genentech/regLM.git
cd regLM
pip install .
```

## Publication

https://genome.cshlp.org/content/early/2024/09/24/gr.279142.124.abstract

<PERSON>, <PERSON>, <PERSON>, D<PERSON>, Bianca<PERSON>, T., & Eraslan, G. (2024). Designing realistic regulatory DNA with autoregressive language models. Genome Research.
